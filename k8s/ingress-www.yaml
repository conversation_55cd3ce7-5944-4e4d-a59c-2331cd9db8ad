apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: clasespl-www
  namespace: production
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  rules:
  - host: www.clases.pl
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: clasespl
            port:
              number: 80
  tls:
  - hosts:
    - www.clases.pl
    secretName: clasespl-tls-secret