spring:
  application:
    name: ClasesPL
  
  datasource:
    url: **************************************************
    username: clasespl_user
    password: clasespl_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
  
  thymeleaf:
    cache: true
    
server:
  port: 8080

logging:
  level:
    com.adrianheras.clasesPl: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate: WARN
  file:
    name: /app/logs/clasespl.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
