apiVersion: batch/v1
kind: CronJob
metadata:
  name: mongodb-backup
  namespace: production
spec:
  schedule: "0 2 * * *"  # Ejecutar a las 2 AM todos los días
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: mongodb-backup
            image: mongo:latest
            command:
            - /bin/sh
            - -c
            - |
              mongodump --uri="${MONGODB_URL}" --gzip --archive=/backup/mongodb-$(date +%Y%m%d_%H%M%S).gz
              aws s3 cp /backup/mongodb-*.gz s3://clasespl-backups/
            env:
            - name: MONGODB_URL
              valueFrom:
                secretKeyRef:
                  name: mongodb-secret
                  key: url
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-key
            volumeMounts:
            - name: backup
              mountPath: /backup
          volumes:
          - name: backup
            emptyDir: {}
          restartPolicy: OnFailure
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-credentials
  namespace: production
type: Opaque
data:
  access-key: YOUR_BASE64_ACCESS_KEY
  secret-key: YOUR_BASE64_SECRET_KEY 