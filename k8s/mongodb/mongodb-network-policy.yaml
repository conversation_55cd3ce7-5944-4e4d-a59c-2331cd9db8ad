apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mongodb-network-policy
  namespace: default
spec:
  podSelector:
    matchLabels:
      statefulset.kubernetes.io/pod-name: mongodb-0
  policyTypes:
    - Ingress
  ingress:
    - from:
        - ipBlock:
            cidr: *************/32
        # ip de mi ordena
        - ipBlock:
            cidr: ************/32
        - podSelector: {}  # Permitir tráfico de cualquier pod en el mismo namespace (default)
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: production  # Permitir desde el namespace "production"
      ports:
        - protocol: TCP
          port: 27017