apiVersion: v1
kind: Service
metadata:
  name: mongodb-external
  namespace: default
spec:
  # El servicio se expone automáticamente en todos los nodos del cluster en el puerto especificado (31702)
  # Al final usas la IP del nodo (tu VPS) con un puerto específico
  type: NodePort
  selector:
    statefulset.kubernetes.io/pod-name: mongodb-0
  ports:
    - port: 27017
      targetPort: 27017
      nodePort: 31702
      protocol: TCP