apiVersion: apps/v1
kind: Deployment
metadata:
  name: clasespl
  namespace: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: clasespl
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: clasespl
    spec:
      terminationGracePeriodSeconds: 60
      imagePullSecrets:
      - name: docker-credentials
      containers:
        - name: clasespl
          image: ${DOCKER_REGISTRY}/clasespl:${BUILD_TAG}
          # Docker descargue la imagen si no está disponible localmente, asegúrate de usar la política adecuada
          # como Always o IfNotPresent.
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: SPRING_DATASOURCE_URL
              value: "**************************************************"
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: postgres-root-username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: postgres-root-password
            - name: SPRING_JPA_HIBERNATE_DDL_AUTO
              value: "update"
            - name: SPRING_JPA_DATABASE_PLATFORM
              value: "org.hibernate.dialect.PostgreSQLDialect"
            - name: SPRING_PROFILES_ACTIVE
              value: "prod"
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            failureThreshold: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3