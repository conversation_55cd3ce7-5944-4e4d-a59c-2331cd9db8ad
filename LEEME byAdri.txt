
- Sube todo a su repo privado de github
    
    se hara: <NAME_EMAIL>:soria86/mirepo1.git

    docker build -t soria86/mirepo1:tagname .

    docker login
  
    docker push soria86/mirepo1:tagname

    mazo comandos de kubectl


- Ten tus credenciales de repo privado de DockerHub (creo que solo un repo privado por cuenta o no .. hmm)


- Jenkins / panel de control / administrar jenkins

  - credentials

    - Obs.: las credenciales del repo git son el token y se incluye a piñón en el script jenkins
  
    - agregar credencial (docker-credentials-id	)(de Docker hub)
    
      - domain: global (en todo Jenkins estén disponibles)
      - store: system
      - id: docker-credentials-id (Este es el ID que utilizarás en el pipeline para referenciar las credenciales.)
      - name: ****
      - username: ____
      - password: _____
      - guardar

    - Añádir credenciales de Kubernetes:
      - igual
      - tipo de credencial: archivo
      - selecciona el archive kubeconfig
      - id: kubeconfig-credentials-id (así será referenciado en el jenkinsfile)
      - guardar


- Crea nueva tarea de Jenkins

    nuevo TAREA

    chollisimos.com

    seccion ˝pipeline"

    Pipeline / definition / pipeline script from SCM
  
      SCM: Git

      origen codigo fuente git:  
        
        https://github.com/adrihm/chollisimos.com.git
        https://<EMAIL>/chollisimos/clasespl.git
- 
- 
  -   TIP: echa un ojo en https://github.com/settings/tokens para ver el valor del token
  - 
      - tokien clasico con permisos de "repo"

              Guardar

              Construir ahopra

              token ______ de github

              rama "main" ? (*/main)

              Apply & save