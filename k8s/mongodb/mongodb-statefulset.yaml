apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb # Specifies the name of the statefulset
spec:
  serviceName: "mongodb-service" # Specifies the service to use
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
        - name: mongodb
          image: mongo:8
          command:
            - mongod
            - "--replSet"
            - rs0
            - "--bind_ip_all"
          ports:
            - containerPort: 27017

          env:
            - name: MONGO_INITDB_ROOT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: mongodb-secret
                  key: mongo-root-username
            - name: MONG<PERSON>_INITDB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mongodb-secret
                  key: mongo-root-password
            - name: MONGO_INITDB_DATABASE
              value: "clasespl"

          volumeMounts:
            - name: mongodb-storage
              mountPath: /data/db
            - name: keyfile
              mountPath: /etc/mongodb-keyfile
              readOnly: true
          resources:
            requests:
              cpu: "100m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
      volumes:
        - name: keyfile
          secret:
            secretName: mongodb-keyfile
            defaultMode: 0400
  volumeClaimTemplates:
    - metadata:
        name: mongodb-storage
      spec:
        accessModes: ["ReadWriteOnce"]
        # ya que así se llama el existente, tras verlo en k get storageclass
        storageClassName: local-path
        resources:
          requests:
            storage: 1Gi