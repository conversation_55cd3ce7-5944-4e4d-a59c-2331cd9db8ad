# Instrucciones para Despliegue Local

## Requisitos Mínimos

- Docker Desktop instalado y en ejecución
- Git

## Instalación Básica

### 1. Clonar el Repositorio

```bash
git clone https://github.com/tu-usuario/clases.pl.git
cd clases.pl
```

### 2. Preparación del Entorno

1. Crear carpetas necesarias:
```bash
mkdir -p var/cache var/log
chmod -R 777 var/
```

2. Crear archivo de variables de entorno:
```bash
cp .env .env.local
```

3. Editar `.env.local` con tus configuraciones:
```env
APP_ENV=dev
APP_SECRET=your_secret_here_change_this
MONGODB_USERNAME=clasespl
MONGODB_PASSWORD=tu_contraseña_segura
MONGODB_DATABASE=clasespl
```

### 3. Construir y Levantar Contenedores

```bash
# Construir las imágenes
docker-compose build --no-cache

# Levantar los contenedores
docker-compose up -d

# Verificar que todos los contenedores estén corriendo
docker-compose ps
```

### 4. Configuración Inicial

```bash
# Instalar dependencias
docker-compose exec app composer install

# Limpiar caché
docker-compose exec app php bin/console cache:clear
```

### 5. Verificar la Instalación

1. La aplicación estará disponible en:
   - http://localhost
   - http://localhost/health (debe devolver "OK")

2. MongoDB estará disponible en:
   - Host: localhost
   - Puerto: 27017

## Comandos Básicos

- Ver logs:
```bash
docker-compose logs -f app
```

- Detener servicios:
```bash
docker-compose down
```

## Solución de Problemas Comunes

### Permisos de Archivos
Si encuentras errores de permisos:
```bash
docker-compose exec app chown -R www-data:www-data var/
docker-compose exec app chmod -R 777 var/
```

### Puertos en Uso
Si los puertos 80 o 27017 están en uso, modifica el `docker-compose.yml`:
```yaml
ports:
  - "8080:80"  # Cambiar 80 por otro puerto
```

### MongoDB no Accesible
1. Verificar que MongoDB esté ejecutándose:
```bash
docker-compose ps
```

2. Verificar logs de MongoDB:
```bash
docker-compose logs mongodb
```

3. Verificar conexión desde la aplicación:
```bash
docker-compose exec app php bin/console doctrine:mongodb:ping
```

## Funcionalidades Opcionales

Las siguientes funcionalidades son opcionales y pueden instalarse según las necesidades del proyecto:

### 1. Tests Unitarios

Para ejecutar los tests:
```bash
# Usando Docker
docker-compose exec app composer install --dev
docker-compose exec app php bin/phpunit
```

### 2. Entorno de Desarrollo

1. Instalar dependencias de desarrollo:
```bash
docker-compose exec app composer install --dev
```

2. Habilitar el profiler de Symfony:
```yaml
# config/packages/dev/web_profiler.yaml
web_profiler:
    toolbar: true
    intercept_redirects: false
```

### 3. Composer Local (opcional)

Si prefieres usar Composer localmente:
1. Instalar Composer desde https://getcomposer.org/
2. Ejecutar:
```bash
composer install
```

## Problem solver
Si da error el MongoDB, desdce el contenedor App:
`mongosh "***********************************************"`


## Recomendaciones Adicionales

- Usar un IDE con soporte para PHP/Symfony (PHPStorm, VSCode)
- Configurar Xdebug para debugging (opcional)
- Implementar pre-commit hooks para control de calidad
- Configurar PHP_CodeSniffer para estándares de código 