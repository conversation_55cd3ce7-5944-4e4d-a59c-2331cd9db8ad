apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: clasespl-redirect
  namespace: production
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    # Redirección permanente a www usando una anotación personalizada
    nginx.ingress.kubernetes.io/permanent-redirect: https://www.clases.pl$request_uri
spec:
  ingressClassName: nginx
  rules:
  - host: clases.pl
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: clasespl
            port:
              number: 80
  tls:
  - hosts:
    - clases.pl
    secretName: clasespl-tls-secret