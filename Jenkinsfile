pipeline {
	agent any

    environment {
        // Variables de configuración de DOCKER HUB
        DOCKER_USERNAME = 'soria86'
        PROJECT_NAME = 'clasespl'

        // Variables de Docker y Registry
		DOCKER_REGISTRY = "docker.io/${DOCKER_USERNAME}"
        DOCKER_CREDENTIALS = 'docker-credentials-id'

        // Variables de Kubernetes
        KUBECONFIG = credentials('kubeconfig-credentials-id')
        K8S_NAMESPACE = 'production'
    }
    
    stages {
		stage('Checkout') {
			steps {
				checkout scm
            }
        }

        stage('Obtener branch') {
			steps {
				script {
					echo "La rama actual es: ${env.BRANCH_NAME}"
                }
            }
        }
        
        stage('Build') {
			steps {
				withCredentials([usernamePassword(credentialsId: DOCKER_CREDENTIALS,
                                                 usernameVariable: 'DOCKER_USER',
                                                 passwordVariable: 'DOCKER_PASS')]) {

                    sh 'echo "$DOCKER_PASS" | docker login -u "$DOCKER_USER" --password-stdin $DOCKER_REGISTRY'

                    sh 'docker build -t $PROJECT_NAME:$BUILD_TAG .'
                    sh 'docker tag $PROJECT_NAME:$BUILD_TAG $DOCKER_USERNAME/$PROJECT_NAME:$BUILD_TAG'
                }
            }
        }
        
        stage('Push') {
			steps {
				sh 'docker push $DOCKER_REGISTRY/$PROJECT_NAME:$BUILD_TAG'
            }
        }
        
        stage('Deploy to Kubernetes') {
			steps {
				withCredentials([usernamePassword(credentialsId: DOCKER_CREDENTIALS,
                                                 usernameVariable: 'DOCKER_USER',
                                                 passwordVariable: 'DOCKER_PASS')]) {

					sh """
							export KUBECONFIG=\${KUBECONFIG}

							# Crear o actualizar el secreto de Docker Hub
							kubectl create secret docker-registry docker-credentials \\
							  --docker-server=\${DOCKER_REGISTRY%/*} \\
							  --docker-username="\${DOCKER_USER}" \\
							  --docker-password="\${DOCKER_PASS}" \\
							  --namespace \${K8S_NAMESPACE} \\
							  --dry-run=client -o yaml | kubectl apply -f -

                           # Crear secreto de PostgreSQL
                           kubectl apply -f k8s/postgresql/postgresql-secret.yaml -n $K8S_NAMESPACE

							# Eliminar pods antiguos que puedan estar bloqueados
							kubectl delete pod -l app=$PROJECT_NAME -n $K8S_NAMESPACE
							
							# Esperar a que se eliminen los pods'
							sleep 10
							
							# Verificar que no hay pods en ejecución'
							kubectl get pods -n $K8S_NAMESPACE -l app=$PROJECT_NAME

							# Esperar a que se eliminen los recursos'
							sleep 5

							# Levantar PostgreSQL
                            kubectl apply -f k8s/postgresql/postgresql-pv-pvc.yaml
                            kubectl apply -f k8s/postgresql/postgresql-service.yaml -n $K8S_NAMESPACE
                            # !! Este crea el pod postgresql-0 (namespace $K8S_NAMESPACE)
                            kubectl apply -f k8s/postgresql/postgresql-statefulset.yaml -n $K8S_NAMESPACE
							
							# Actualizar imagen en deployment'
							sed -i 's|\${DOCKER_REGISTRY}|${DOCKER_REGISTRY}|g' k8s/deployment.yaml
							sed -i 's|\${BUILD_TAG}|${BUILD_TAG}|g' k8s/deployment.yaml

							kubectl apply -f k8s/metallb.yaml

							# Aplicar deployment y service'
							kubectl apply -f k8s/deployment.yaml
							kubectl apply -f k8s/service.yaml
							kubectl apply -f k8s/ingress-single.yaml
							kubectl apply -f k8s/postgresql/postgresql-external.yaml -n $K8S_NAMESPACE
							kubectl apply -f k8s/prod_issuer.yaml
							kubectl apply -f k8s/custom-certificate.yaml


							# Reiniciar el deployment para forzar pull de la imagen'
							# Reiniciar pods:
							kubectl rollout restart deployment $PROJECT_NAME -n $K8S_NAMESPACE

							"""

				}
            }
        }
    }
    
    post {
		always {
			cleanWs()
        }
        success {
			echo 'Pipeline ejecutado exitosamente!'
        }
        failure {
			echo 'El pipeline ha fallado'
        }
    }
} 