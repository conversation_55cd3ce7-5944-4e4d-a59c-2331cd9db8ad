# NOTAS

### 3. Acceso a la Aplicación

1. Obtener la IP externa:
```bash
kubectl get service chollisimos -n production -w
# Esperar hasta que EXTERNAL-IP esté asignada
```

# Verificar health check
curl http://<EXTERNAL-IP>/health

# Verificar la aplicación web
curl -I http://<EXTERNAL-IP>/

# Escalar a más réplicas
kubectl scale deployment chollisimos -n production --replicas=3

# Verificar el escalado
kubectl get pods -n production -w


### 2. Problemas de Conexión a MongoDB
```bash
# Verificar secretos
kubectl get secrets mongodb-secret -n production -o yaml

# Verificar servicio de MongoDB
kubectl get service mongodb -n production

# Probar conexión desde un pod temporal
kubectl run mongodb-test --rm -it --image=mongo -- mongo mongodb://usuario:contraseñ**************************************:27017/chollisimos
```

### 3. Problemas de Red
```bash
# Verificar servicios
kubectl get endpoints -n production

# Verificar políticas de red
kubectl get networkpolicies -n production
```

## Funcionalidades Opcionales

Las siguientes características son opcionales y pueden implementarse según las necesidades del proyecto:

### 1. Monitorización con Prometheus y Grafana

1. Crear namespace:
```bash
kubectl create namespace monitoring
```

2. Verificar requisitos:
```bash
# Verificar StorageClass para PVC
kubectl get storageclass
```

3. Aplicar configuraciones:
```bash
kubectl apply -f k8s/monitoring/prometheus-config.yaml
kubectl apply -f k8s/monitoring/grafana.yaml

# Verificar despliegue
kubectl get pods -n monitoring
kubectl get services -n monitoring
```

4. Acceder a los dashboards:
- Prometheus: http://<PROMETHEUS-IP>:9090
- Grafana: http://<GRAFANA-IP>:3000

### 2. Sistema de Logs con ELK Stack

1. Requisitos:
- Mínimo 4GB de RAM disponible
- StorageClass configurada

2. Desplegar ELK:
```bash
kubectl create namespace logging
kubectl apply -f k8s/logging/elastic-stack.yaml

# Verificar despliegue
kubectl get pods -n logging
kubectl get services -n logging
```

3. Acceder a Kibana:
- URL: http://<KIBANA-IP>:5601
- Credenciales por defecto: elastic/changeme

### 3. Backups Automáticos

1. Configurar credenciales AWS:
```bash
# Verificar bucket S3
aws s3 ls s3://chollisimos-backups/

# Crear secretos
kubectl create secret generic aws-credentials \
  --from-literal=access-key=YOUR_ACCESS_KEY \
  --from-literal=secret-key=YOUR_SECRET_KEY \
  -n production

# Aplicar configuración
kubectl apply -f k8s/backup/mongodb-backup.yaml
```

2. Verificar la configuración:
```bash
kubectl get cronjobs -n production
kubectl get jobs -n production
```

## Recomendaciones para Producción

### Seguridad
- Implementar Network Policies
- Configurar RBAC
- Usar secretos encriptados
- Habilitar audit logging

### Alta Disponibilidad
- Configurar múltiples réplicas
- Usar Pod Disruption Budgets
- Implementar health checks
- Configurar auto-scaling

### Networking
- Configurar Ingress con TLS
- Implementar cert-manager
- Configurar políticas de retención de logs
- Monitorizar el tráfico de red

### Mantenimiento
- Programar actualizaciones
- Configurar políticas de backup
- Monitorizar uso de recursos
- Implementar estrategias de rollback 