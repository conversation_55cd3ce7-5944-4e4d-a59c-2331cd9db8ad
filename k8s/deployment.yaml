apiVersion: apps/v1
kind: Deployment
metadata:
  name: clasespl
  namespace: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: clasespl
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: clasespl
    spec:
      terminationGracePeriodSeconds: 60
      imagePullSecrets:
      - name: docker-credentials
      containers:
        - name: clasespl
          image: ${DOCKER_REGISTRY}/clasespl:${BUILD_TAG}
          # Docker descargue la imagen si no está disponible localmente, asegúrate de usar la política adecuada
          # como Always o IfNotPresent.
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          env:
            - name: MONGODB_USERNAME
              value: "admin"
#              valueFrom:
#                secretKeyRef:
#                  name: mongodb-secret
#                  key: username
            - name: MONGODB_PASSWORD
              value: "fdkiomhdDSFFCDe34432"
#              valueFrom:
#                secretKeyRef:
#                  name: mongodb-secret
#                  key: password
            - name: MONGODB_DATABASE
              value: "clasespl"
#              valueFrom:
#                configMapKeyRef:
#                  name: mongodb-config
#                  key: database
            - name: APP_ENV
              value: "prod"
            - name: APP_DEBUG
              value: "0"
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          startupProbe:
            httpGet:
              path: /
              port: 80
            failureThreshold: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          command: ["/bin/sh"]
          args: ["-c", "php-fpm -D && sleep 5 && nginx -g 'daemon off;'"] 