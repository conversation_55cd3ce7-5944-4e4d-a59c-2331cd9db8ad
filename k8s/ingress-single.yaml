apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: clasespl
  namespace: production
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    # Configuración para redireccionar clases.pl a www.clases.pl
    nginx.ingress.kubernetes.io/configuration-snippet: |
      if ($host = 'clases.pl') {
        return 301 $scheme://www.clases.pl$request_uri;
      }
spec:
  ingressClassName: nginx
  rules:
  - host: clases.pl
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: clasespl
            port:
              number: 80
  - host: www.clases.pl
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: clasespl
            port:
              number: 80
  tls:
  - hosts:
    - clases.pl
    - www.clases.pl
    secretName: clasespl-tls-secret